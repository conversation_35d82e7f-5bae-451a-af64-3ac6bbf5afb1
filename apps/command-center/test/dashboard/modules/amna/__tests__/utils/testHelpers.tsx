import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { type RenderOptions, render } from '@testing-library/react'
import type React from 'react'
import { AutomationMode, type IAmnaConfig } from '../../../../src/modules/amna/amna.service'
import type { IAgent, ISystemHealth, ITask, IWorkflow } from '../../types/amna.types'

// Create a custom render function that includes providers
export function renderWithProviders(
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
    },
  })

  function Wrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  }

  return render(ui, { wrapper: Wrapper, ...options })
}

// Mock data generators
export const createMockAgent = (overrides?: Partial<IAgent>): IAgent => ({
  id: 'agent-test-1',
  name: 'Test Agent',
  type: 'TaskAgent',
  status: 'idle',
  capabilities: ['task_execution', 'data_processing'],
  description: 'A test agent for unit tests',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

export const createMockWorkflow = (overrides?: Partial<IWorkflow>): IWorkflow => ({
  id: 'workflow-test-1',
  name: 'Test Workflow',
  description: 'A test workflow for unit tests',
  status: 'idle',
  agents: ['agent-1', 'agent-2'],
  tasks: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  startedAt: null,
  completedAt: null,
  ...overrides,
})

export const createMockTask = (overrides?: Partial<ITask>): ITask => ({
  id: 'task-test-1',
  workflowId: 'workflow-test-1',
  agentId: 'agent-test-1',
  name: 'Test Task',
  status: 'pending',
  input: { test: 'data' },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  startedAt: null,
  completedAt: null,
  ...overrides,
})

export const createMockSystemHealth = (overrides?: Partial<ISystemHealth>): ISystemHealth => ({
  status: 'healthy',
  services: {
    api: { status: 'healthy', message: 'API is running' },
    database: { status: 'healthy', message: 'Database connected' },
    redis: { status: 'healthy', message: 'Redis connected' },
    ollama: { status: 'healthy', message: 'Ollama available' },
  },
  metrics: {
    cpu: 50.0,
    memory: 60.0,
    disk: 70.0,
    activeAgents: 5,
    runningWorkflows: 2,
    queuedTasks: 10,
  },
  timestamp: new Date().toISOString(),
  ...overrides,
})

export const createMockConfig = (overrides?: Partial<IAmnaConfig>): IAmnaConfig => ({
  // Automation settings
  automationMode: AutomationMode.SEMI,
  maxAutoActions: 10,
  requireApproval: [],
  enabledFeatures: ['agents', 'tasks', 'workflows'],

  general: {
    autoClusteringEnabled: true,
    memoryClusteringThreshold: 100,
    maxAgents: 10,
    maxConcurrentWorkflows: 5,
  },
  llm: {
    defaultProvider: 'openai',
    defaultModel: 'gpt-4',
    temperature: 0.7,
    maxTokens: 4000,
  },
  security: {
    apiRateLimit: 100,
    maxFileSize: 10485760,
    allowedFileTypes: ['.txt', '.pdf'],
  },
  features: {
    enableAgentCollaboration: true,
    enableAdvancedMemory: true,
    enableWorkflowCheckpoints: true,
  },
  ...overrides,
})

// WebSocket mock helpers
export class MockWebSocket {
  private listeners: Map<string, Set<Function>> = new Map()
  public connected = false

  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)?.add(callback)
    return this
  }

  off(event: string, callback: Function) {
    if (this.listeners.has(event)) {
      this.listeners.get(event)?.delete(callback)
    }
    return this
  }

  emit(event: string, data?: any) {
    if (this.listeners.has(event)) {
      this.listeners.get(event)?.forEach((callback) => callback(data))
    }
  }

  connect() {
    this.connected = true
    this.emit('connect')
  }

  disconnect() {
    this.connected = false
    this.emit('disconnect')
  }
}

// Async helpers
export const waitForMs = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms))

// Error helpers
export const createApiError = (message: string, status: number = 400) => ({
  response: {
    status,
    data: {
      error: {
        message,
        code: 'TEST_ERROR',
      },
    },
  },
})

// Mock file helpers
export const createMockFile = (
  name: string,
  content: string,
  type: string = 'text/plain'
): File => {
  const blob = new Blob([content], { type })
  return new File([blob], name, { type })
}

// Date helpers
export const mockDateNow = (timestamp: number) => {
  const originalDateNow = Date.now
  Date.now = jest.fn(() => timestamp)
  return () => {
    Date.now = originalDateNow
  }
}
