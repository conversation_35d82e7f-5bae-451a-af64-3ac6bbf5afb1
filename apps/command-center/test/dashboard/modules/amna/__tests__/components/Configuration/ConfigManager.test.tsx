import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { fireEvent, render, screen, waitFor } from '@testing-library/react'
import toast from 'react-hot-toast'
import { type IAmnaConfig } from '../../../../../src/modules/amna/amna.service'
import { ConfigManager } from '../../../components/Configuration/ConfigManager'
import { amnaApi } from '../../../services/amnaApi'

jest.mock('../../../services/amnaApi')
jest.mock('react-hot-toast')
jest.mock('@monaco-editor/react', () => ({
  __esModule: true,
  default: jest.fn(({ value, onChange }) => (
    <textarea
      data-testid="monaco-editor"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  )),
}))

const mockAmnaApi = amnaApi as jest.Mocked<typeof amnaApi>
const mockToast = toast as jest.Mocked<typeof toast>

describe('ConfigManager Component', () => {
  let queryClient: QueryClient

  const mockConfig: IAmnaConfig = {
    // Automation settings
    automationMode: 'SEMI' as any,
    maxAutoActions: 10,
    requireApproval: [],
    enabledFeatures: ['agents', 'tasks', 'workflows'],

    general: {
      autoClusteringEnabled: true,
      memoryClusteringThreshold: 100,
      maxAgents: 10,
      maxConcurrentWorkflows: 5,
    },
    llm: {
      defaultProvider: 'openai',
      defaultModel: 'gpt-4',
      temperature: 0.7,
      maxTokens: 4000,
    },
    security: {
      apiRateLimit: 100,
      maxFileSize: 10485760,
      allowedFileTypes: ['.txt', '.pdf', '.doc'],
    },
    features: {
      enableAgentCollaboration: true,
      enableAdvancedMemory: true,
      enableWorkflowCheckpoints: true,
      enableCodeExecution: false,
      enableWebSearch: true,
    },
  }

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
      },
    })

    jest.clearAllMocks()

    mockAmnaApi.getConfiguration.mockResolvedValue({ success: true, data: mockConfig })
    mockAmnaApi.updateConfiguration.mockResolvedValue({ success: true } as any)
  })

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <ConfigManager />
      </QueryClientProvider>
    )
  }

  it('should render configuration manager', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Configuration Management')).toBeInTheDocument()
    })
  })

  it('should display all configuration sections', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('General')).toBeInTheDocument()
      expect(screen.getByText('LLM Settings')).toBeInTheDocument()
      expect(screen.getByText('Security')).toBeInTheDocument()
      expect(screen.getByText('Feature Flags')).toBeInTheDocument()
      expect(screen.getByText('Raw JSON')).toBeInTheDocument()
    })
  })

  it('should switch between sections', async () => {
    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('General Settings')).toBeInTheDocument()
    })

    fireEvent.click(screen.getByText('LLM Settings'))
    expect(screen.getByLabelText('Default Provider')).toBeInTheDocument()

    fireEvent.click(screen.getByText('Security'))
    expect(screen.getByLabelText('API Rate Limit (requests/minute)')).toBeInTheDocument()

    fireEvent.click(screen.getByText('Feature Flags'))
    expect(screen.getByText('Agent Collaboration')).toBeInTheDocument()
  })

  describe('General Settings', () => {
    it('should display and update general settings', async () => {
      renderComponent()

      await waitFor(() => {
        expect(screen.getByLabelText('Auto Clustering')).toBeInTheDocument()
      })

      const autoClusteringCheckbox = screen.getByLabelText('Auto Clustering')
      expect(autoClusteringCheckbox).toBeChecked()

      const maxAgentsInput = screen.getByLabelText('Max Agents')
      expect(maxAgentsInput).toHaveValue(10)

      fireEvent.change(maxAgentsInput, { target: { value: '20' } })
      expect(maxAgentsInput).toHaveValue(20)

      await waitFor(() => {
        expect(screen.getByText('Unsaved changes')).toBeInTheDocument()
      })
    })
  })

  describe('LLM Settings', () => {
    it('should display and update LLM settings', async () => {
      renderComponent()

      await waitFor(() => {
        fireEvent.click(screen.getByText('LLM Settings'))
      })

      const providerSelect = screen.getByLabelText('Default Provider')
      expect(providerSelect).toHaveValue('openai')

      fireEvent.change(providerSelect, { target: { value: 'ollama' } })
      expect(providerSelect).toHaveValue('ollama')

      const temperatureInput = screen.getByLabelText('Temperature')
      expect(temperatureInput).toHaveValue(0.7)

      fireEvent.change(temperatureInput, { target: { value: '0.9' } })
      expect(temperatureInput).toHaveValue(0.9)
    })
  })

  describe('Security Settings', () => {
    it('should display and update security settings', async () => {
      renderComponent()

      await waitFor(() => {
        fireEvent.click(screen.getByText('Security'))
      })

      const rateLimitInput = screen.getByLabelText('API Rate Limit (requests/minute)')
      expect(rateLimitInput).toHaveValue(100)

      fireEvent.change(rateLimitInput, { target: { value: '200' } })
      expect(rateLimitInput).toHaveValue(200)

      const maxFileSizeInput = screen.getByLabelText('Max File Size (MB)')
      expect(maxFileSizeInput).toHaveValue(10)

      fireEvent.change(maxFileSizeInput, { target: { value: '20' } })
      expect(maxFileSizeInput).toHaveValue(20)
    })

    it('should update allowed file types', async () => {
      renderComponent()

      await waitFor(() => {
        fireEvent.click(screen.getByText('Security'))
      })

      const fileTypesTextarea = screen.getByPlaceholderText('One file type per line (e.g., .txt)')
      expect(fileTypesTextarea).toHaveValue('.txt\n.pdf\n.doc')

      fireEvent.change(fileTypesTextarea, { target: { value: '.txt\n.pdf\n.doc\n.xlsx' } })
      expect(fileTypesTextarea).toHaveValue('.txt\n.pdf\n.doc\n.xlsx')
    })
  })

  describe('Feature Flags', () => {
    it('should display and toggle feature flags', async () => {
      renderComponent()

      await waitFor(() => {
        fireEvent.click(screen.getByText('Feature Flags'))
      })

      expect(screen.getByText('Agent Collaboration')).toBeInTheDocument()
      expect(screen.getByText('Advanced Memory')).toBeInTheDocument()
      expect(screen.getByText('Code Execution')).toBeInTheDocument()

      // Find toggle buttons by their parent elements
      const collaborationToggle = screen
        .getByText('Agent Collaboration')
        .closest('div')
        ?.querySelector('button')

      fireEvent.click(collaborationToggle!)

      await waitFor(() => {
        expect(screen.getByText('Unsaved changes')).toBeInTheDocument()
      })
    })

    it('should use quick actions for feature flags', async () => {
      renderComponent()

      await waitFor(() => {
        fireEvent.click(screen.getByText('Feature Flags'))
      })

      const enableAllButton = screen.getByText('Enable All')
      fireEvent.click(enableAllButton)

      await waitFor(() => {
        expect(screen.getByText('Unsaved changes')).toBeInTheDocument()
      })

      const disableAllButton = screen.getByText('Disable All')
      fireEvent.click(disableAllButton)

      const recommendedButton = screen.getByText('Recommended Settings')
      fireEvent.click(recommendedButton)
    })
  })

  describe('Raw JSON Editor', () => {
    it('should display and edit raw JSON', async () => {
      renderComponent()

      await waitFor(() => {
        fireEvent.click(screen.getByText('Raw JSON'))
      })

      const editor = screen.getByTestId('monaco-editor')
      expect(editor).toBeInTheDocument()

      const configJson = JSON.stringify(mockConfig, null, 2)
      expect(editor).toHaveValue(configJson)

      const newConfig = { ...mockConfig, general: { ...mockConfig.general, maxAgents: 20 } }
      fireEvent.change(editor, { target: { value: JSON.stringify(newConfig, null, 2) } })

      await waitFor(() => {
        expect(screen.getByText('Valid JSON')).toBeInTheDocument()
      })
    })

    it('should show error for invalid JSON', async () => {
      renderComponent()

      await waitFor(() => {
        fireEvent.click(screen.getByText('Raw JSON'))
      })

      const editor = screen.getByTestId('monaco-editor')
      fireEvent.change(editor, { target: { value: '{ invalid json' } })

      await waitFor(() => {
        expect(screen.getByText('Invalid JSON')).toBeInTheDocument()
      })
    })
  })

  describe('Save and Reset', () => {
    it('should save configuration changes', async () => {
      renderComponent()

      await waitFor(() => {
        expect(screen.getByText('General Settings')).toBeInTheDocument()
      })

      const maxAgentsInput = screen.getByLabelText('Max Agents')
      fireEvent.change(maxAgentsInput, { target: { value: '20' } })

      await waitFor(() => {
        expect(screen.getByText('Unsaved changes')).toBeInTheDocument()
      })

      const saveButton = screen.getByText('Save Changes')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(mockAmnaApi.updateConfiguration).toHaveBeenCalledWith(
          expect.objectContaining({
            general: expect.objectContaining({ maxAgents: 20 }),
          })
        )
        expect(mockToast.success).toHaveBeenCalledWith('Configuration updated successfully')
      })
    })

    it('should reset configuration changes', async () => {
      renderComponent()

      await waitFor(() => {
        expect(screen.getByText('General Settings')).toBeInTheDocument()
      })

      const maxAgentsInput = screen.getByLabelText('Max Agents')
      fireEvent.change(maxAgentsInput, { target: { value: '20' } })

      await waitFor(() => {
        expect(screen.getByText('Unsaved changes')).toBeInTheDocument()
      })

      const resetButton = screen.getByTitle('Reset Changes')
      fireEvent.click(resetButton)

      await waitFor(() => {
        expect(maxAgentsInput).toHaveValue(10)
        expect(screen.queryByText('Unsaved changes')).not.toBeInTheDocument()
      })
    })

    it('should handle save errors', async () => {
      const _error = new Error('Failed to save')
      mockAm_error.updateConfiguration.mockRejectedValue({
        response: { data: { error: { message: 'Save failed' } } },
      })

      renderComponent()

      await waitFor(() => {
        expect(screen.getByText('General Settings')).toBeInTheDocument()
      })

      const maxAgentsInput = screen.getByLabelText('Max Agents')
      fireEvent.change(maxAgentsInput, { target: { value: '20' } })

      const saveButton = screen.getByText('Save Changes')
      fireEvent.click(saveButton)

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Save failed')
      })
    })
  })

  it('should show loading state', () => {
    mockAmnaApi.getConfiguration.mockImplementation(() => new Promise(() => {}))

    renderComponent()

    expect(screen.getByText('Loading configuration...')).toBeInTheDocument()
  })

  it('should show error state', async () => {
    mockAmnaApi.getConfiguration.mockRejectedValue(new Error('Failed to load'))

    renderComponent()

    await waitFor(() => {
      expect(screen.getByText('Error loading configuration')).toBeInTheDocument()
    })
  })
})
