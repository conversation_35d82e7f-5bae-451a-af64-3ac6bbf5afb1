import axios from 'axios'
import type { IAmnaConfig } from '../../../../src/modules/amna/amna.service'
import { AmnaApiService } from '../../services/amnaApi'
import type { IAgent, ISystemHealth, IWorkflow } from '../../types/amna.types'

jest.mock('axios')
const mockedAxios = axios as jest.Mocked<typeof axios>

describe('AmnaApiService', () => {
  let apiService: AmnaApiService

  beforeEach(() => {
    apiService = new AmnaApiService('http://localhost:3000/api/amna', 'test-token')
    jest.clearAllMocks()
  })

  describe('Agent Operations', () => {
    it('should fetch agents with pagination', async () => {
      const mockAgents: IAgent[] = [
        {
          id: 'agent-1',
          name: 'Test Agent',
          type: 'TaskAgent',
          status: 'idle',
          capabilities: ['task_execution'],
          description: 'Test agent',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue({
          data: {
            data: mockAgents,
            pagination: { page: 1, limit: 10, total: 1 },
          },
        }),
      } as any)

      const result = await apiService.getAgents({ page: 1, limit: 10 })

      expect(result.data).toEqual(mockAgents)
      expect(result.pagination).toEqual({ page: 1, limit: 10, total: 1 })
    })

    it('should create a new agent', async () => {
      const newAgent = {
        name: 'New Agent',
        type: 'TaskAgent' as const,
        capabilities: ['task_execution'],
        description: 'New test agent',
      }

      const createdAgent: IAgent = {
        id: 'agent-2',
        ...newAgent,
        status: 'idle',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({ data: { data: createdAgent } }),
      } as any)

      const result = await apiService.createAgent(newAgent)

      expect(result.data).toEqual(createdAgent)
    })

    it('should update an agent', async () => {
      const agentId = 'agent-1'
      const updates = { name: 'Updated Agent' }

      const updatedAgent: IAgent = {
        id: agentId,
        name: 'Updated Agent',
        type: 'TaskAgent',
        status: 'idle',
        capabilities: ['task_execution'],
        description: 'Test agent',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      mockedAxios.create.mockReturnValue({
        patch: jest.fn().mockResolvedValue({ data: { data: updatedAgent } }),
      } as any)

      const result = await apiService.updateAgent(agentId, updates)

      expect(result.data).toEqual(updatedAgent)
    })

    it('should delete an agent', async () => {
      const agentId = 'agent-1'

      mockedAxios.create.mockReturnValue({
        delete: jest.fn().mockResolvedValue({ data: { success: true } }),
      } as any)

      await apiService.deleteAgent(agentId)

      expect(mockedAxios.create().delete).toHaveBeenCalledWith(`/agents/${agentId}`)
    })

    it('should execute agent tasks', async () => {
      const agentId = 'agent-1'
      const task = { action: 'test', parameters: { foo: 'bar' } }
      const mockResult = { taskId: 'task-1', status: 'queued' }

      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({ data: { data: mockResult } }),
      } as any)

      const result = await apiService.executeAgentTask(agentId, task)

      expect(result.data).toEqual(mockResult)
    })
  })

  describe('Workflow Operations', () => {
    it('should fetch workflows', async () => {
      const mockWorkflows: IWorkflow[] = [
        {
          id: 'workflow-1',
          name: 'Test Workflow',
          description: 'Test workflow',
          status: 'idle',
          agents: ['agent-1'],
          tasks: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          startedAt: null,
          completedAt: null,
        },
      ]

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue({
          data: {
            data: mockWorkflows,
            pagination: { page: 1, limit: 10, total: 1 },
          },
        }),
      } as any)

      const result = await apiService.getWorkflows()

      expect(result.data).toEqual(mockWorkflows)
    })

    it('should start a workflow', async () => {
      const workflowId = 'workflow-1'
      const mockResult = { workflowId, status: 'running' }

      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({ data: { data: mockResult } }),
      } as any)

      const result = await apiService.startWorkflow(workflowId)

      expect(result.data).toEqual(mockResult)
    })

    it('should stop a workflow', async () => {
      const workflowId = 'workflow-1'
      const mockResult = { workflowId, status: 'stopped' }

      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({ data: { data: mockResult } }),
      } as any)

      const result = await apiService.stopWorkflow(workflowId)

      expect(result.data).toEqual(mockResult)
    })
  })

  describe('System Health Operations', () => {
    it('should fetch system health', async () => {
      const mockHealth: ISystemHealth = {
        status: 'healthy',
        services: {
          api: { status: 'healthy', message: 'API is running' },
          database: { status: 'healthy', message: 'Database connected' },
          redis: { status: 'healthy', message: 'Redis connected' },
          ollama: { status: 'healthy', message: 'Ollama available' },
        },
        metrics: {
          cpu: 45.5,
          memory: 60.2,
          disk: 75.0,
          activeAgents: 5,
          runningWorkflows: 2,
          queuedTasks: 10,
        },
        timestamp: new Date().toISOString(),
      }

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue({ data: { data: mockHealth } }),
      } as any)

      const result = await apiService.getSystemHealth()

      expect(result.data).toEqual(mockHealth)
    })

    it('should fetch memory summary', async () => {
      const mockSummary = {
        totalEntries: 100,
        categories: {
          conversation: 40,
          task: 30,
          system: 30,
        },
        recentActivity: [],
      }

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue({ data: { data: mockSummary } }),
      } as any)

      const result = await apiService.getMemorySummary()

      expect(result.data).toEqual(mockSummary)
    })
  })

  describe('Configuration Operations', () => {
    it('should fetch configuration', async () => {
      const mockConfig: IAmnaConfig = {
        // Automation settings
        automationMode: AutomationMode.SEMI,
        maxAutoActions: 10,
        requireApproval: [],
        enabledFeatures: ['agents', 'tasks', 'workflows'],

        general: {
          autoClusteringEnabled: true,
          memoryClusteringThreshold: 100,
          maxAgents: 10,
          maxConcurrentWorkflows: 5,
        },
        llm: {
          defaultProvider: 'openai',
          defaultModel: 'gpt-4',
          temperature: 0.7,
          maxTokens: 4000,
        },
        security: {
          apiRateLimit: 100,
          maxFileSize: 10485760,
          allowedFileTypes: ['.txt', '.pdf'],
        },
        features: {
          enableAgentCollaboration: true,
          enableAdvancedMemory: true,
          enableWorkflowCheckpoints: true,
        },
      }

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue({ data: { data: mockConfig } }),
      } as any)

      const result = await apiService.getConfiguration()

      expect(result.data).toEqual(mockConfig)
    })

    it('should update configuration', async () => {
      const updates = {
        general: { maxAgents: 20 },
      }

      mockedAxios.create.mockReturnValue({
        patch: jest.fn().mockResolvedValue({ data: { success: true } }),
      } as any)

      await apiService.updateConfiguration(updates)

      expect(mockedAxios.create().patch).toHaveBeenCalledWith('/config', updates)
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors', async () => {
      const errorResponse = {
        response: {
          status: 404,
          data: { error: { message: 'Agent not found' } },
        },
      }

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockRejectedValue(errorResponse),
      } as any)

      await expect(apiService.getAgent('invalid-id')).rejects.toEqual(errorResponse)
    })

    it('should handle network errors', async () => {
      const networkError = new Error('Network Error')

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockRejectedValue(networkError),
      } as any)

      await expect(apiService.getAgents()).rejects.toThrow('Network Error')
    })
  })
})
