import axios, { type AxiosInstance } from 'axios'
import type { IAmnaConfig } from '../../../src/modules/amna/amna.service'
import type {
  IAgent,
  IAmnaApiResponse,
  ICreateAgentDto,
  ICreateWorkflowDto,
  IMemory,
  IMemorySearchQuery,
  IMemorySearchResult,
  ISystemHealth,
  ITask,
  IWorkflow,
} from '../types/amna.types'

export class AmnaApiService {
  private api: AxiosInstance

  constructor(baseURL: string = 'http://localhost:3000', token?: string) {
    this.api = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    })

    // Add request interceptor for auth
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('auth-token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })

    // Add response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  // Agent Management
  async getAgents(params?: { page?: number; limit?: number; status?: string }) {
    const response = await this.api.get<IAmnaApiResponse<IAgent[]>>('/api/amna/agents', { params })
    return response.data
  }

  async getAgent(id: string) {
    const response = await this.api.get<IAmnaApiResponse<IAgent>>(`/api/amna/agents/${id}`)
    return response.data
  }

  async createAgent(data: ICreateAgentDto) {
    const response = await this.api.post<IAmnaApiResponse<IAgent>>('/api/amna/agents', data)
    return response.data
  }

  async updateAgent(id: string, data: Partial<IAgent>) {
    const response = await this.api.put<IAmnaApiResponse<IAgent>>(`/api/amna/agents/${id}`, data)
    return response.data
  }

  async deleteAgent(id: string) {
    const response = await this.api.delete<IAmnaApiResponse<void>>(`/api/amna/agents/${id}`)
    return response.data
  }

  async restartAgent(id: string) {
    const response = await this.api.post<IAmnaApiResponse<IAgent>>(`/api/amna/agents/${id}/restart`)
    return response.data
  }

  // Workflow Management
  async getWorkflows(params?: { page?: number; limit?: number; status?: string }) {
    const response = await this.api.get<IAmnaApiResponse<IWorkflow[]>>('/api/amna/workflows', {
      params,
    })
    return response.data
  }

  async getWorkflow(id: string) {
    const response = await this.api.get<IAmnaApiResponse<IWorkflow>>(`/api/amna/workflows/${id}`)
    return response.data
  }

  async createWorkflow(data: ICreateWorkflowDto) {
    const response = await this.api.post<IAmnaApiResponse<IWorkflow>>('/api/amna/workflows', data)
    return response.data
  }

  async executeWorkflow(id: string, input?: Record<string, any>) {
    const response = await this.api.post<IAmnaApiResponse<{ executionId: string }>>(
      `/api/amna/workflows/${id}/execute`,
      { input }
    )
    return response.data
  }

  async pauseWorkflow(id: string, executionId: string) {
    const response = await this.api.post<IAmnaApiResponse<void>>(
      `/api/amna/workflows/${id}/executions/${executionId}/pause`
    )
    return response.data
  }

  async resumeWorkflow(id: string, executionId: string) {
    const response = await this.api.post<IAmnaApiResponse<void>>(
      `/api/amna/workflows/${id}/executions/${executionId}/resume`
    )
    return response.data
  }

  async cancelWorkflow(id: string, executionId: string) {
    const response = await this.api.post<IAmnaApiResponse<void>>(
      `/api/amna/workflows/${id}/executions/${executionId}/cancel`
    )
    return response.data
  }

  // Task Management
  async getTasks(params?: { page?: number; limit?: number; status?: string; agentId?: string }) {
    const response = await this.api.get<IAmnaApiResponse<ITask[]>>('/api/amna/tasks', { params })
    return response.data
  }

  async getTask(id: string) {
    const response = await this.api.get<IAmnaApiResponse<ITask>>(`/api/amna/tasks/${id}`)
    return response.data
  }

  async createTask(data: Partial<ITask>) {
    const response = await this.api.post<IAmnaApiResponse<ITask>>('/api/amna/tasks', data)
    return response.data
  }

  // Memory Management
  async searchMemories(query: IMemorySearchQuery) {
    const response = await this.api.post<IAmnaApiResponse<IMemorySearchResult[]>>(
      '/api/amna/memory/search',
      query
    )
    return response.data
  }

  async getMemory(id: string) {
    const response = await this.api.get<IAmnaApiResponse<IMemory>>(`/api/amna/memory/${id}`)
    return response.data
  }

  async storeMemory(content: string, metadata: any, options?: any) {
    const response = await this.api.post<IAmnaApiResponse<IMemory>>('/api/amna/memory', {
      content,
      metadata,
      options,
    })
    return response.data
  }

  async deleteMemory(filters: { olderThan?: string; category?: string; retention?: string }) {
    const response = await this.api.delete<IAmnaApiResponse<{ deleted: number }>>(
      '/api/amna/memory',
      { data: filters }
    )
    return response.data
  }

  async getMemorySummary() {
    const response = await this.api.get<IAmnaApiResponse<any>>('/api/amna/memory/summary')
    return response.data
  }

  // System Management
  async getSystemStatus() {
    const response = await this.api.get<IAmnaApiResponse<ISystemHealth>>('/api/amna/status')
    return response.data
  }

  async getSystemMetrics() {
    const response = await this.api.get<IAmnaApiResponse<any>>('/api/amna/metrics')
    return response.data
  }

  async getPerformanceMetrics() {
    const response = await this.api.get<IAmnaApiResponse<any>>('/api/amna/metrics/performance')
    return response.data
  }

  async getConfiguration() {
    const response = await this.api.get<IAmnaApiResponse<IAmnaConfig>>('/api/amna/config')
    return response.data
  }

  async updateConfiguration(config: Partial<IAmnaConfig>) {
    const response = await this.api.put<IAmnaApiResponse<IAmnaConfig>>('/api/amna/config', config)
    return response.data
  }

  // Tool Management
  async getTools() {
    const response = await this.api.get<IAmnaApiResponse<any[]>>('/api/amna/tools')
    return response.data
  }

  async getTool(name: string) {
    const response = await this.api.get<IAmnaApiResponse<any>>(`/api/amna/tools/${name}`)
    return response.data
  }

  async registerTool(tool: any) {
    const response = await this.api.post<IAmnaApiResponse<any>>('/api/amna/tools', tool)
    return response.data
  }

  // Health Check
  async healthCheck() {
    const response = await this.api.get<{ status: string; timestamp: string }>('/health')
    return response.data
  }
}

// Export singleton instance
export const amnaApi = new AmnaApiService()
