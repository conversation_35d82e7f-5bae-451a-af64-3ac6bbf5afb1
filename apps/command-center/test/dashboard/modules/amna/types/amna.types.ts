// AMNA Dashboard Types

export type AgentStatus = 'idle' | 'busy' | 'error' | 'offline'
export type AgentType = 'TaskAgent' | 'SimpleAgent' | 'OrchestrationAgent'
export type WorkflowType = 'sequential' | 'parallel' | 'conditional' | 'hierarchical'
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
export type MemoryCategory = 'episodic' | 'semantic' | 'procedural' | 'working' | 'autobiographical'

export interface IAgent {
  id: string
  name: string
  description?: string
  type: AgentType
  status: AgentStatus
  capabilities: string[]
  currentTasks: string[]
  config: IAgentConfig
  metadata: {
    createdAt: string
    updatedAt: string
    lastActiveAt: string
    totalTasks: number
    successRate: number
  }
}

export interface IAgentConfig {
  llmProvider?: 'openai' | 'ollama'
  llmModel?: string
  temperature?: number
  maxTokens?: number
  maxConcurrentTasks?: number
  memoryEnabled?: boolean
  tools?: string[]
}

export interface ITask {
  id: string
  title: string
  description?: string
  type: string
  status: TaskStatus
  assignedAgent?: string
  dependencies?: string[]
  input?: Record<string, any>
  output?: Record<string, any>
  error?: string
  progress?: number
  startTime?: string
  endTime?: string
  duration?: number
}

export interface IWorkflow {
  id: string
  name: string
  description?: string
  type: WorkflowType
  status: 'draft' | 'active' | 'running' | 'completed' | 'failed' | 'paused'
  tasks: ITask[]
  agents: string[]
  config: IWorkflowConfig
  metadata: {
    createdAt: string
    updatedAt: string
    lastRunAt?: string
    totalRuns: number
    successRate: number
    averageDuration: number
  }
}

export interface IWorkflowConfig {
  maxRetries?: number
  timeout?: number
  checkpointEnabled?: boolean
  parallelLimit?: number
  errorHandling?: 'stop' | 'continue' | 'retry'
}

export interface IMemory {
  id: string
  content: string
  embedding?: number[]
  metadata: IMemoryMetadata
  category: MemoryCategory
  importance: number
  agentId?: string
  workflowId?: string
  tags: string[]
  timestamp: string
  accessCount: number
  lastAccessed: string
}

export interface IMemoryMetadata {
  source?: string
  contentType?: string
  language?: string
  sessionId?: string
  [key: string]: any
}

export interface ISystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  services: IServiceHealth[]
  metrics: ISystemMetrics
  lastCheck: string
}

export interface IServiceHealth {
  name: string
  status: 'up' | 'down' | 'degraded'
  responseTime?: number
  error?: string
  details?: Record<string, any>
}

export interface ISystemMetrics {
  cpu: number
  memory: number
  disk: number
  activeAgents: number
  runningWorkflows: number
  taskThroughput: number
  averageResponseTime: number
}

export interface IPerformanceMetrics {
  timestamp: string
  cpu: number
  memory: number
  disk: number
  networkIn: number
  networkOut: number
}

export interface IMemorySummary {
  totalEntries: number
  categories: {
    conversation: number
    task: number
    system: number
    workflow: number
  }
  recentActivity: Array<{
    type: 'add' | 'cluster' | 'remove'
    category: string
    timestamp: string
    description: string
  }>
  stats: {
    averageRetrievalTime: number
    cacheHitRate: number
    compressionRatio: number
  }
}

// IAmnaConfig moved to main service file to avoid duplication

// WebSocket Event Types
export interface IAmnaWebSocketEvent {
  type: AmnaEventType
  data: any
  timestamp: string
}

export type AmnaEventType =
  | 'agent.created'
  | 'agent.updated'
  | 'agent.deleted'
  | 'agent.status_changed'
  | 'task.created'
  | 'task.started'
  | 'task.progress'
  | 'task.completed'
  | 'task.failed'
  | 'workflow.created'
  | 'workflow.started'
  | 'workflow.progress'
  | 'workflow.completed'
  | 'workflow.failed'
  | 'workflow.paused'
  | 'workflow.resumed'
  | 'memory.stored'
  | 'memory.retrieved'
  | 'memory.clustered'
  | 'system.health_changed'
  | 'system.alert'
  | 'system.metric_update'

// API Response Types
export interface IAmnaApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  metadata?: {
    page?: number
    limit?: number
    total?: number
  }
}

export interface ICreateAgentDto {
  name: string
  type: AgentType
  capabilities?: string[]
  config?: IAgentConfig
}

export interface ICreateWorkflowDto {
  name: string
  description?: string
  type: WorkflowType
  tasks: ICreateTaskDto[]
  agents?: string[]
  config?: IWorkflowConfig
}

export interface ICreateTaskDto {
  id?: string
  title: string
  description?: string
  type: string
  assignedAgent?: string
  dependencies?: string[]
  input?: Record<string, any>
}

export interface IMemorySearchQuery {
  text?: string
  category?: MemoryCategory
  agentId?: string
  workflowId?: string
  tags?: string[]
  startDate?: string
  endDate?: string
  limit?: number
  threshold?: number
}

export interface IMemorySearchResult {
  memory: IMemory
  score: number
  highlights?: string[]
}
