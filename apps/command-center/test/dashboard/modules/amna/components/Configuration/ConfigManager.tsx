import { useMutation, useQuery } from '@tanstack/react-query'
import {
  AlertCircle,
  Cpu,
  FileJson,
  RefreshCw,
  Save,
  Settings,
  Shield,
  ToggleLeft,
} from 'lucide-react'
import type React from 'react'
import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import type { IAmnaConfig } from '../../../../src/modules/amna/amna.service'
import { amnaApi } from '../../services/amnaApi'
import { FeatureFlags } from './FeatureFlags'
import { SettingsEditor } from './SettingsEditor'

export const ConfigManager: React.FC = () => {
  const [activeSection, setActiveSection] = useState<
    'general' | 'llm' | 'security' | 'features' | 'raw'
  >('general')
  const [hasChanges, setHasChanges] = useState(false)
  const [localConfig, setLocalConfig] = useState<IAmnaConfig | null>(null)

  // Fetch configuration
  const {
    data: configResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['amna', 'config'],
    queryFn: () => amnaApi.getConfiguration(),
  })

  // Update configuration mutation
  const updateConfigMutation = useMutation({
    mutationFn: (config: Partial<IAmnaConfig>) => amnaApi.updateConfiguration(config),
    onSuccess: () => {
      toast.success('Configuration updated successfully')
      setHasChanges(false)
      refetch()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error?.message || 'Failed to update configuration')
    },
  })

  useEffect(() => {
    if (configResponse?.data) {
      setLocalConfig(configResponse.data)
    }
  }, [configResponse])

  const handleConfigChange = (section: keyof IAmnaConfig, updates: any) => {
    if (!localConfig) return

    setLocalConfig({
      ...localConfig,
      [section]: {
        ...localConfig[section],
        ...updates,
      },
    })
    setHasChanges(true)
  }

  const handleSave = () => {
    if (!localConfig) return
    updateConfigMutation.mutate(localConfig)
  }

  const handleReset = () => {
    if (configResponse?.data) {
      setLocalConfig(configResponse.data)
      setHasChanges(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <Settings className="mx-auto mb-2 h-8 w-8 animate-spin text-blue-500" />
          <p className="text-gray-500">Loading configuration...</p>
        </div>
      </div>
    )
  }

  if (error || !localConfig) {
    return (
      <div className="flex items-center gap-2 rounded-lg bg-red-50 p-4 text-red-700">
        <AlertCircle className="h-5 w-5" />
        <span>Error loading configuration</span>
      </div>
    )
  }

  const sections = [
    { id: 'general' as const, label: 'General', icon: Settings },
    { id: 'llm' as const, label: 'LLM Settings', icon: Cpu },
    { id: 'security' as const, label: 'Security', icon: Shield },
    { id: 'features' as const, label: 'Feature Flags', icon: ToggleLeft },
    { id: 'raw' as const, label: 'Raw JSON', icon: FileJson },
  ]

  return (
    <div className="rounded-lg bg-white shadow">
      <div className="border-gray-200 border-b p-6">
        <div className="flex items-center justify-between">
          <h2 className="font-bold text-gray-900 text-xl">Configuration Management</h2>
          <div className="flex items-center gap-2">
            {hasChanges && (
              <span className="flex items-center gap-1 text-sm text-yellow-600">
                <AlertCircle className="h-4 w-4" />
                Unsaved changes
              </span>
            )}
            <button
              onClick={handleReset}
              disabled={!hasChanges}
              className="rounded-lg p-2 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700 disabled:cursor-not-allowed disabled:opacity-50"
              title="Reset Changes"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
            <button
              onClick={handleSave}
              disabled={!hasChanges || updateConfigMutation.isPending}
              className="flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
            >
              <Save className="h-4 w-4" />
              Save Changes
            </button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 border-gray-200 border-r p-4">
          <nav className="space-y-1">
            {sections.map((section) => {
              const Icon = section.icon
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`flex w-full items-center gap-3 rounded-lg px-3 py-2 transition-colors ${
                    activeSection === section.id
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{section.label}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          {activeSection === 'general' && (
            <div className="space-y-6">
              <h3 className="mb-4 font-semibold text-gray-900 text-lg">General Settings</h3>

              <div className="space-y-4">
                <div>
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={localConfig.general.autoClusteringEnabled}
                      onChange={(e) =>
                        handleConfigChange('general', {
                          autoClusteringEnabled: e.target.checked,
                        })
                      }
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <div>
                      <span className="font-medium text-gray-900">Auto Clustering</span>
                      <p className="text-gray-500 text-sm">Automatically cluster memory entries</p>
                    </div>
                  </label>
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    Memory Clustering Threshold
                  </label>
                  <input
                    type="number"
                    min="10"
                    max="1000"
                    value={localConfig.general.memoryClusteringThreshold}
                    onChange={(e) =>
                      handleConfigChange('general', {
                        memoryClusteringThreshold: parseInt(e.target.value),
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="mt-1 text-gray-500 text-xs">
                    Number of memories before automatic clustering
                  </p>
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">Max Agents</label>
                  <input
                    type="number"
                    min="1"
                    max="100"
                    value={localConfig.general.maxAgents}
                    onChange={(e) =>
                      handleConfigChange('general', {
                        maxAgents: parseInt(e.target.value),
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    Max Concurrent Workflows
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="50"
                    value={localConfig.general.maxConcurrentWorkflows}
                    onChange={(e) =>
                      handleConfigChange('general', {
                        maxConcurrentWorkflows: parseInt(e.target.value),
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {activeSection === 'llm' && (
            <div className="space-y-6">
              <h3 className="mb-4 font-semibold text-gray-900 text-lg">LLM Settings</h3>

              <div className="space-y-4">
                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    Default Provider
                  </label>
                  <select
                    value={localConfig.llm.defaultProvider}
                    onChange={(e) =>
                      handleConfigChange('llm', {
                        defaultProvider: e.target.value as 'openai' | 'ollama',
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="openai">OpenAI</option>
                    <option value="ollama">Ollama (Local)</option>
                  </select>
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    Default Model
                  </label>
                  <input
                    type="text"
                    value={localConfig.llm.defaultModel}
                    onChange={(e) =>
                      handleConfigChange('llm', {
                        defaultModel: e.target.value,
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., gpt-4-turbo-preview"
                  />
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    Temperature
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="2"
                    step="0.1"
                    value={localConfig.llm.temperature}
                    onChange={(e) =>
                      handleConfigChange('llm', {
                        temperature: parseFloat(e.target.value),
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="mt-1 text-gray-500 text-xs">0 = More focused, 2 = More creative</p>
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">Max Tokens</label>
                  <input
                    type="number"
                    min="100"
                    max="32000"
                    value={localConfig.llm.maxTokens}
                    onChange={(e) =>
                      handleConfigChange('llm', {
                        maxTokens: parseInt(e.target.value),
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {activeSection === 'security' && (
            <div className="space-y-6">
              <h3 className="mb-4 font-semibold text-gray-900 text-lg">Security Settings</h3>

              <div className="space-y-4">
                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    API Rate Limit (requests/minute)
                  </label>
                  <input
                    type="number"
                    min="10"
                    max="1000"
                    value={localConfig.security.apiRateLimit}
                    onChange={(e) =>
                      handleConfigChange('security', {
                        apiRateLimit: parseInt(e.target.value),
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    Max File Size (MB)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="1000"
                    value={localConfig.security.maxFileSize / (1024 * 1024)}
                    onChange={(e) =>
                      handleConfigChange('security', {
                        maxFileSize: parseInt(e.target.value) * 1024 * 1024,
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="mb-1 block font-medium text-gray-700 text-sm">
                    Allowed File Types
                  </label>
                  <textarea
                    value={localConfig.security.allowedFileTypes.join('\n')}
                    onChange={(e) =>
                      handleConfigChange('security', {
                        allowedFileTypes: e.target.value.split('\n').filter(Boolean),
                      })
                    }
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                    placeholder="One file type per line (e.g., .txt)"
                  />
                </div>
              </div>
            </div>
          )}

          {activeSection === 'features' && (
            <FeatureFlags
              features={localConfig.features}
              onChange={(features) => handleConfigChange('features', features)}
            />
          )}

          {activeSection === 'raw' && (
            <SettingsEditor
              config={localConfig}
              onChange={setLocalConfig}
              onHasChanges={setHasChanges}
            />
          )}
        </div>
      </div>
    </div>
  )
}
