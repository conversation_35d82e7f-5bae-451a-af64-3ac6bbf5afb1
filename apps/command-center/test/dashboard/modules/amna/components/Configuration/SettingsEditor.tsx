import MonacoEditor from '@monaco-editor/react'
import { <PERSON>ert<PERSON><PERSON>cle, CheckCircle, Copy, Download, FileJson, Upload } from 'lucide-react'
import type React from 'react'
import { useEffect, useState } from 'react'
import toast from 'react-hot-toast'
import type { IAmnaConfig } from '../../../../src/modules/amna/amna.service'

interface SettingsEditorProps {
  config: IAmnaConfig
  onChange: (config: IAmnaConfig) => void
  onHasChanges: (hasChanges: boolean) => void
}

export const SettingsEditor: React.FC<SettingsEditorProps> = ({
  config,
  onChange,
  onHasChanges,
}) => {
  const [jsonString, setJsonString] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isValid, setIsValid] = useState(true)

  useEffect(() => {
    setJsonString(JSON.stringify(config, null, 2))
  }, [config])

  const handleEditorChange = (value: string | undefined) => {
    if (!value) return

    setJsonString(value)

    try {
      const parsed = JSON.parse(value)
      setError(null)
      setIsValid(true)
      onChange(parsed)
      onHasChanges(true)
    } catch (e) {
      setError(e instanceof Error ? e.message : 'Invalid JSON')
      setIsValid(false)
    }
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(jsonString)
    toast.success('Configuration copied to clipboard')
  }

  const handleDownload = () => {
    const blob = new Blob([jsonString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'amna-config.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Configuration downloaded')
  }

  const handleUpload = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      try {
        const text = await file.text()
        const parsed = JSON.parse(text)
        onChange(parsed)
        onHasChanges(true)
        toast.success('Configuration loaded from file')
      } catch (_error) {
        toast.error('Failed to load configuration file')
      }
    }
    input.click()
  }

  return (
    <div className="space-y-4">
      <div>
        <div className="mb-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileJson className="h-5 w-5 text-gray-500" />
            <h3 className="font-semibold text-gray-900 text-lg">Raw Configuration</h3>
          </div>

          <div className="flex items-center gap-2">
            {!isValid && (
              <div className="flex items-center gap-2 text-red-600">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm">Invalid JSON</span>
              </div>
            )}
            {isValid && (
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="h-4 w-4" />
                <span className="text-sm">Valid JSON</span>
              </div>
            )}

            <button
              onClick={handleCopy}
              className="rounded-lg p-2 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
              title="Copy to clipboard"
            >
              <Copy className="h-4 w-4" />
            </button>

            <button
              onClick={handleDownload}
              className="rounded-lg p-2 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
              title="Download as JSON"
            >
              <Download className="h-4 w-4" />
            </button>

            <button
              onClick={handleUpload}
              className="rounded-lg p-2 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
              title="Upload JSON file"
            >
              <Upload className="h-4 w-4" />
            </button>
          </div>
        </div>

        <p className="mb-4 text-gray-600 text-sm">
          Edit the raw JSON configuration. Changes will be validated in real-time.
        </p>
      </div>

      <div className="relative">
        <div
          className={`overflow-hidden rounded-lg border-2 ${
            isValid ? 'border-gray-200' : 'border-red-300'
          }`}
        >
          <MonacoEditor
            height="500px"
            language="json"
            theme="vs-light"
            value={jsonString}
            onChange={handleEditorChange}
            options={{
              minimap: { enabled: false },
              fontSize: 14,
              lineNumbers: 'on',
              scrollBeyondLastLine: false,
              automaticLayout: true,
              formatOnPaste: true,
              formatOnType: true,
            }}
          />
        </div>

        {error && (
          <div className="absolute right-0 bottom-0 left-0 border-red-200 border-t bg-red-50 p-2">
            <p className="flex items-center gap-2 text-red-700 text-sm">
              <AlertCircle className="h-4 w-4" />
              {error}
            </p>
          </div>
        )}
      </div>

      <div className="rounded-lg bg-blue-50 p-4">
        <h4 className="mb-2 font-medium text-blue-900">Configuration Schema</h4>
        <div className="space-y-1 text-blue-700 text-sm">
          <p>
            • <code>general</code>: Core system settings (clustering, limits)
          </p>
          <p>
            • <code>llm</code>: Language model configuration
          </p>
          <p>
            • <code>security</code>: Security policies and restrictions
          </p>
          <p>
            • <code>features</code>: Feature flags (boolean values)
          </p>
        </div>
      </div>

      <div className="rounded-lg bg-yellow-50 p-4">
        <h4 className="mb-2 flex items-center gap-2 font-medium text-yellow-900">
          <AlertCircle className="h-4 w-4" />
          Warning
        </h4>
        <p className="text-sm text-yellow-700">
          Be careful when editing the raw configuration. Invalid settings may cause system
          instability. Always test changes in a development environment first.
        </p>
      </div>
    </div>
  )
}
