import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { Type } from 'class-transformer'
import { IsEnum, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator'
import { AutomationMode } from '../amna.service'

export enum ExecutionType {
  AGENT = 'agent',
  TASK = 'task',
  WORKFLOW = 'workflow',
}

export class AutomationDto {
  @ApiPropertyOptional({ enum: AutomationMode })
  @IsOptional()
  @IsEnum(AutomationMode)
  mode?: AutomationMode

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  maxActions?: number
}

export class ExecuteDto {
  @ApiProperty({ enum: ExecutionType })
  @IsEnum(ExecutionType)
  type: ExecutionType

  @ApiProperty()
  @IsString()
  name: string

  @ApiProperty()
  input: any

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  config?: any

  @ApiPropertyOptional({ type: AutomationDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => AutomationDto)
  automation?: AutomationDto
}
