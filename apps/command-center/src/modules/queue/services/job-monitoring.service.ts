import { InjectQueue } from '@nestjs/bull';
import { Injectable, type OnModuleInit } from '@nestjs/common';
import type { EventEmitter2 } from '@nestjs/event-emitter';
import type { Job, Queue } from 'bull';
import type { NotificationService } from '../../notifications/notification.service';
import type { RedisService } from '../../redis/redis.service';

export interface JobAlert {
  type: 'failure' | 'stalled' | 'slow' | 'backlog';
  queueName: string;
  jobId?: string;
  message: string;
  metadata?: any;
  timestamp: Date;
}

export interface JobHealthStatus {
  queueName: string;
  status: 'healthy' | 'degraded' | 'critical';
  metrics: {
    processingRate: number;
    failureRate: number;
    averageTime: number;
    backlogSize: number;
  };
  alerts: JobAlert[];
}

export interface FailurePattern {
  pattern: string;
  count: number;
  lastSeen: Date;
  jobs: string[];
}

@Injectable()
export class JobMonitoringService implements OnModuleInit {
  private readonly MONITORING_WINDOW = 300000; // 5 minutes
  private readonly FAILURE_THRESHOLD = 0.1; // 10% failure rate
  private readonly SLOW_JOB_THRESHOLD = 60000; // 1 minute
  private readonly BACKLOG_THRESHOLD = 1000;

  private jobMetrics: Map<string, Map<string, any>> = new Map();
  private failurePatterns: Map<string, FailurePattern> = new Map();
  private activeAlerts: Map<string, JobAlert> = new Map();

  constructor(
    @InjectQueue('file-processing') private _fileQueue: Queue,
    @InjectQueue('embeddings') private _embeddingQueue: Queue,
    @InjectQueue('document-parsing') private _documentQueue: Queue,
    private readonly eventEmitter: EventEmitter2,
    private readonly redisService: RedisService,
    private readonly notificationService: NotificationService,
  ) {}

  onModuleInit() {
    this.setupQueueMonitoring(this.fileQueue, 'file-processing');
    this.setupQueueMonitoring(this.embeddingQueue, 'embeddings');
    this.setupQueueMonitoring(this.documentQueue, 'document-parsing');

    // Start periodic health checks
    setInterval(() => this.performHealthCheck(), 60000); // Every minute
  }

  private setupQueueMonitoring(queue: Queue, name: string) {
    // Monitor job events
    queue.on('completed', (job: Job, result: any) => {
      this.recordJobCompletion(name, job, result);
    });

    queue.on('failed', (job: Job, err: Error) => {
      this.handleJobFailure(name, job, err);
    });

    queue.on('stalled', (job: Job) => {
      this.handleStalledJob(name, job);
    });

    queue.on('progress', (job: Job, progress: number) => {
      this.updateJobProgress(name, job, progress);
    });

    queue.on('active', (job: Job) => {
      this.recordJobStart(name, job);
    });

    queue.on('waiting', (jobId: string) => {
      this.recordJobWaiting(name, jobId);
    });

    queue.on('drained', () => {
      console.log(`Queue ${name} has been drained`);
    });
  }

  async getQueueHealth(queueName?: string): Promise<JobHealthStatus[]> {
    const queues = queueName
      ? [{ name: queueName, queue: this.getQueue(queueName) }]
      : [
          { name: 'file-processing', queue: this.fileQueue },
          { name: 'embeddings', queue: this.embeddingQueue },
          { name: 'document-parsing', queue: this.documentQueue },
        ];

    const healthStatuses = await Promise.all(
      queues.map(async ({ name, queue }) => {
        const metrics = await this.calculateQueueMetrics(name, queue);
        const status = this.determineHealthStatus(metrics);
        const alerts = this.getActiveAlerts(name);

        return {
          queueName: name,
          status,
          metrics,
          alerts,
        };
      }),
    );

    return healthStatuses;
  }

  async getFailurePatterns(queueName?: string): Promise<FailurePattern[]> {
    if (queueName) {
      return Array.from(this.failurePatterns.values()).filter((pattern) =>
        pattern.pattern.startsWith(queueName),
      );
    }
    return Array.from(this.failurePatterns.values());
  }

  async getJobDetails(queueName: string, jobId: string): Promise<any> {
    const queue = this.getQueue(queueName);
    const job = await queue.getJob(jobId);

    if (!job) {
      return null;
    }

    const logs = await queue.getJobLogs(jobId);

    return {
      id: job.id,
      name: job.name,
      data: job.data,
      opts: job.opts,
      progress: job.progress(),
      attemptsMade: job.attemptsMade,
      finishedOn: job.finishedOn,
      processedOn: job.processedOn,
      failedReason: job.failedReason,
      stacktrace: job.stacktrace,
      returnvalue: job.returnvalue,
      logs: logs.logs,
    };
  }

  async retryFailedJob(queueName: string, jobId: string): Promise<boolean> {
    try {
      const queue = this.getQueue(queueName);
      const job = await queue.getJob(jobId);

      if (!job || !job.failedReason) {
        return false;
      }

      await job.retry();

      // Clear failure pattern if exists
      const patternKey = `${queueName}:${job.failedReason}`;
      this.failurePatterns.delete(patternKey);

      return true;
    } catch (error) {
      console.error(
        `Failed to retry job ${jobId} in queue ${queueName}:`,
        error,
      );
      return false;
    }
  }

  async pauseQueue(queueName: string): Promise<void> {
    const queue = this.getQueue(queueName);
    await queue.pause();

    this.createAlert({
      type: 'backlog',
      queueName,
      message: `Queue ${queueName} has been paused`,
      timestamp: new Date(),
    });
  }

  async resumeQueue(queueName: string): Promise<void> {
    const queue = this.getQueue(queueName);
    await queue.resume();

    this.removeAlert(`${queueName}:paused`);
  }

  async drainQueue(queueName: string): Promise<void> {
    const queue = this.getQueue(queueName);
    await queue.empty();
  }

  // Event handlers
  private recordJobStart(queueName: string, job: Job) {
    const metrics = this.getQueueMetrics(queueName);
    metrics.set(`job:${job.id}:start`, Date.now());
  }

  private recordJobCompletion(queueName: string, job: Job, result: any) {
    const metrics = this.getQueueMetrics(queueName);
    const startTime = metrics.get(`job:${job.id}:start`);

    if (startTime) {
      const duration = Date.now() - startTime;
      this.updateProcessingMetrics(queueName, duration, true);

      // Clean up job metrics
      metrics.delete(`job:${job.id}:start`);
      metrics.delete(`job:${job.id}:progress`);
    }

    // Emit success event
    this.eventEmitter.emit('job.completed', {
      queue: queueName,
      jobId: job.id,
      duration: Date.now() - (startTime || Date.now()),
      result,
    });
  }

  private handleJobFailure(queueName: string, job: Job, error: Error) {
    const metrics = this.getQueueMetrics(queueName);
    const startTime = metrics.get(`job:${job.id}:start`);

    if (startTime) {
      const duration = Date.now() - startTime;
      this.updateProcessingMetrics(queueName, duration, false);
    }

    // Track failure pattern
    this.trackFailurePattern(queueName, job, error);

    // Create alert for repeated failures
    if (job.attemptsMade >= (job.opts.attempts || 3)) {
      this.createAlert({
        type: 'failure',
        queueName,
        jobId: job.id?.toString(),
        message: `Job ${job.id} failed after ${job.attemptsMade} attempts: ${error.message}`,
        metadata: {
          jobName: job.name,
          error: error.message,
          stack: error.stack,
        },
        timestamp: new Date(),
      });
    }

    // Emit failure event
    this.eventEmitter.emit('job.failed', {
      queue: queueName,
      jobId: job.id,
      error: error.message,
      attempts: job.attemptsMade,
    });
  }

  private handleStalledJob(queueName: string, job: Job) {
    this.createAlert({
      type: 'stalled',
      queueName,
      jobId: job.id?.toString(),
      message: `Job ${job.id} stalled in queue ${queueName}`,
      metadata: {
        jobName: job.name,
        data: job.data,
      },
      timestamp: new Date(),
    });

    // Emit stalled event
    this.eventEmitter.emit('job.stalled', {
      queue: queueName,
      jobId: job.id,
    });
  }

  private updateJobProgress(queueName: string, job: Job, progress: number) {
    const metrics = this.getQueueMetrics(queueName);
    const startTime = metrics.get(`job:${job.id}:start`);

    if (startTime) {
      const elapsed = Date.now() - startTime;

      // Check if job is running slow
      if (elapsed > this.SLOW_JOB_THRESHOLD && progress < 50) {
        this.createAlert({
          type: 'slow',
          queueName,
          jobId: job.id?.toString(),
          message: `Job ${job.id} is running slow (${progress}% after ${elapsed}ms)`,
          metadata: {
            progress,
            elapsed,
            jobName: job.name,
          },
          timestamp: new Date(),
        });
      }
    }

    metrics.set(`job:${job.id}:progress`, progress);
  }

  private recordJobWaiting(queueName: string, _jobId: string) {
    const metrics = this.getQueueMetrics(queueName);
    const waitingCount = metrics.get('waiting') || 0;
    metrics.set('waiting', waitingCount + 1);
  }

  // Metrics and monitoring
  private async calculateQueueMetrics(
    queueName: string,
    queue: Queue,
  ): Promise<any> {
    const [waitingCount, activeCount, completedCount, failedCount] =
      await Promise.all([
        queue.getWaitingCount(),
        queue.getActiveCount(),
        queue.getCompletedCount(),
        queue.getFailedCount(),
      ]);

    const metrics = this.getQueueMetrics(queueName);
    const recentCompleted = metrics.get('recentCompleted') || 0;
    const recentFailed = metrics.get('recentFailed') || 0;
    const totalProcessingTime = metrics.get('totalProcessingTime') || 0;
    const processedCount = metrics.get('processedCount') || 1;

    const processingRate =
      recentCompleted / (this.MONITORING_WINDOW / 1000 / 60); // per minute
    const failureRate = recentFailed / (recentCompleted + recentFailed) || 0;
    const averageTime = totalProcessingTime / processedCount;
    const backlogSize = waitingCount + activeCount;

    return {
      processingRate,
      failureRate,
      averageTime,
      backlogSize,
      waiting: waitingCount,
      active: activeCount,
      completed: completedCount,
      failed: failedCount,
    };
  }

  private determineHealthStatus(
    metrics: any,
  ): 'healthy' | 'degraded' | 'critical' {
    if (metrics.failureRate > this.FAILURE_THRESHOLD * 2) {
      return 'critical';
    }

    if (
      metrics.failureRate > this.FAILURE_THRESHOLD ||
      metrics.backlogSize > this.BACKLOG_THRESHOLD ||
      metrics.averageTime > this.SLOW_JOB_THRESHOLD
    ) {
      return 'degraded';
    }

    return 'healthy';
  }

  private async performHealthCheck() {
    const healthStatuses = await this.getQueueHealth();

    for (const status of healthStatuses) {
      if (status.status === 'critical') {
        await this.notificationService.sendRealTimeNotification('system', {
          type: 'error',
          title: 'Queue Critical',
          message: `Queue ${status.queueName} is in critical state`,
          app: 'command-center',
          priority: 'urgent',
          metadata: status,
        });
      } else if (status.status === 'degraded') {
        console.warn(`Queue ${status.queueName} is degraded:`, status.metrics);
      }

      // Check for backlog
      if (status.metrics.backlogSize > this.BACKLOG_THRESHOLD) {
        this.createAlert({
          type: 'backlog',
          queueName: status.queueName,
          message: `Queue ${status.queueName} has a large backlog (${status.metrics.backlogSize} jobs)`,
          metadata: status.metrics,
          timestamp: new Date(),
        });
      }
    }
  }

  // Helper methods
  private getQueue(name: string): Queue {
    switch (name) {
      case 'file-processing':
        return this.fileQueue;
      case 'embeddings':
        return this.embeddingQueue;
      case 'document-parsing':
        return this.documentQueue;
      default:
        throw new Error(`Unknown queue: ${name}`);
    }
  }

  private getQueueMetrics(queueName: string): Map<string, any> {
    if (!this.jobMetrics.has(queueName)) {
      this.jobMetrics.set(queueName, new Map());
    }
    return this.jobMetrics.get(queueName)!;
  }

  private updateProcessingMetrics(
    queueName: string,
    duration: number,
    success: boolean,
  ) {
    const metrics = this.getQueueMetrics(queueName);

    // Update recent counts
    const recentCompleted = metrics.get('recentCompleted') || 0;
    const recentFailed = metrics.get('recentFailed') || 0;

    if (success) {
      metrics.set('recentCompleted', recentCompleted + 1);
    } else {
      metrics.set('recentFailed', recentFailed + 1);
    }

    // Update processing time
    const totalTime = metrics.get('totalProcessingTime') || 0;
    const count = metrics.get('processedCount') || 0;

    metrics.set('totalProcessingTime', totalTime + duration);
    metrics.set('processedCount', count + 1);

    // Reset counters periodically
    if (
      !metrics.has('lastReset') ||
      Date.now() - metrics.get('lastReset') > this.MONITORING_WINDOW
    ) {
      metrics.set('recentCompleted', 0);
      metrics.set('recentFailed', 0);
      metrics.set('lastReset', Date.now());
    }
  }

  private trackFailurePattern(queueName: string, job: Job, error: Error) {
    const patternKey = `${queueName}:${error.message}`;
    const existing = this.failurePatterns.get(patternKey);

    if (existing) {
      existing.count++;
      existing.lastSeen = new Date();
      existing.jobs.push(job.id?.toString() || '');

      // Keep only last 10 job IDs
      if (existing.jobs.length > 10) {
        existing.jobs = existing.jobs.slice(-10);
      }
    } else {
      this.failurePatterns.set(patternKey, {
        pattern: error.message,
        count: 1,
        lastSeen: new Date(),
        jobs: [job.id?.toString() || ''],
      });
    }
  }

  private createAlert(alert: JobAlert) {
    const key = `${alert.queueName}:${alert.type}:${alert.jobId || 'queue'}`;
    this.activeAlerts.set(key, alert);

    // Emit alert event
    this.eventEmitter.emit('queue.alert', alert);
  }

  private removeAlert(key: string) {
    this.activeAlerts.delete(key);
  }

  private getActiveAlerts(queueName: string): JobAlert[] {
    return Array.from(this.activeAlerts.values()).filter(
      (alert) => alert.queueName === queueName,
    );
  }
}
